{"name": "ajv-keywords", "version": "3.2.0", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^6.0.0"}, "devDependencies": {"ajv": "^6.0.0", "ajv-pack": "^0.3.0", "chai": "^4.0.2", "coveralls": "^3.0.0", "dot": "^1.1.1", "eslint": "^4.9.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.7.4", "json-schema-test": "^2.0.0", "mocha": "^5.0.0", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}}