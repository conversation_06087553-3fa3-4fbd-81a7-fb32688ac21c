{"author": "\"Cowboy\" <PERSON> (http://benalman.com/)", "name": "async-foreach", "description": "An optionally-asynchronous forEach with an interesting interface.", "version": "0.1.3", "homepage": "http://github.com/cowboy/javascript-sync-async-foreach", "bugs": "https://github.com/cowboy/javascript-sync-async-foreach/issues", "repository": {"type": "git", "url": "git://github.com/cowboy/javascript-sync-async-foreach.git"}, "main": "lib/foreach", "engines": {"node": "*"}, "keywords": ["array", "loop", "sync", "async", "foreach"], "dependencies": {}, "devDependencies": {}}