<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="JavaScriptSettings">
    <option name="languageLevel" value="ES6" />
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State>
            <id />
          </State>
          <State>
            <id>General</id>
          </State>
          <State>
            <id>PHP</id>
          </State>
          <State>
            <id>UndefinedPHP</id>
          </State>
          <State>
            <id>UnusedPHP</id>
          </State>
          <State>
            <id>XML</id>
          </State>
          <State>
            <id>XPath</id>
          </State>
        </expanded-state>
      </profile-state>
    </entry>
  </component>
  <component name="masterDetails">
    <states>
      <state key="ScopeChooserConfigurable.UI">
        <settings>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
    </states>
  </component>
</project>