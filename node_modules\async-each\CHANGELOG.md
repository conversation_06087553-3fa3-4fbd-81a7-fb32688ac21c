# async-each 1.0.0 (26 November 2015)
* Bumped version to 1.0.0 (no functional changes)

# async-each 0.1.6 (5 November 2014)
* Add license to package.json

# async-each 0.1.5 (22 October 2014)
* Clean up package.json to fix npm warning about `repo`

# async-each 0.1.4 (12 November 2013)
* Fixed AMD definition.

# async-each 0.1.3 (25 July 2013)
* Fixed double wrapping of errors.

# async-each 0.1.2 (7 July 2013)
* Fixed behaviour on empty arrays.

# async-each 0.1.1 (14 June 2013)
* Wrapped function in closure, enabled strict mode.

# async-each 0.1.0 (14 June 2013)
* Initial release.
