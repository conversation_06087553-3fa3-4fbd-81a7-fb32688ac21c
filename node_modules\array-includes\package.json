{"name": "array-includes", "version": "3.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "description": "An ES7/ES2016 spec-compliant `Array.prototype.includes` shim/polyfill/replacement that works as far down as ES3.", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run --silent lint && evalmd README.md", "test": "npm run --silent tests-only", "posttest": "npm run --silent security", "tests-only": "es-shim-api --bound && npm run --silent test:shimmed && npm run --silent test:module", "test:shimmed": "node test/shimmed.js", "test:module": "node test/index.js", "coverage": "covert test/*.js", "coverage:quiet": "covert test/*.js --quiet", "lint": "npm run --silent jscs && npm run --silent eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "security": "nsp check"}, "repository": {"type": "git", "url": "git://github.com/ljharb/array-includes.git"}, "keywords": ["Array.prototype.includes", "includes", "array", "ES7", "shim", "polyfill", "contains", "Array.prototype.contains", "es-shim API"], "dependencies": {"define-properties": "^1.1.2", "es-abstract": "^1.7.0"}, "devDependencies": {"foreach": "^2.0.5", "function-bind": "^1.1.0", "tape": "^4.6.3", "indexof": "^0.0.1", "covert": "^1.1.0", "jscs": "^3.0.7", "nsp": "^2.6.3", "eslint": "^3.19.0", "@ljharb/eslint-config": "^11.0.0", "semver": "^5.3.0", "replace": "^0.3.0", "@es-shims/api": "^1.2.0", "evalmd": "^0.0.17"}, "testling": {"files": ["test/index.js", "test/shimmed.js"], "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}}