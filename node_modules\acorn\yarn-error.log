Arguments: 
  /home/<USER>/prog/node/bin/node /home/<USER>/.yarn/bin/yarn.js publish

PATH: 
  /home/<USER>/.yarn/bin:/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:/home/<USER>/prog/node/bin:/home/<USER>/bin:/usr/sbin:/sbin:./node_modules/.bin

Yarn version: 
  1.3.2

Node version: 
  9.5.0

Platform: 
  linux x64

npm manifest: 
  {
    "name": "acorn",
    "description": "ECMAScript parser",
    "homepage": "https://github.com/acornjs/acorn",
    "main": "dist/acorn.js",
    "module": "dist/acorn.es.js",
    "version": "5.5.3",
    "engines": {
      "node": ">=0.4.0"
    },
    "maintainers": [
      {
        "name": "Marijn Haverbeke",
        "email": "<EMAIL>",
        "web": "http://marijnhaverbeke.nl"
      },
      {
        "name": "<PERSON><PERSON><PERSON>",
        "email": "<EMAIL>",
        "web": "http://rreverser.com/"
      }
    ],
    "repository": {
      "type": "git",
      "url": "https://github.com/acornjs/acorn.git"
    },
    "license": "MIT",
    "scripts": {
      "prepare": "npm test",
      "test": "node test/run.js && node test/lint.js",
      "pretest": "npm run build:main && npm run build:loose",
      "test:test262": "node bin/run_test262.js",
      "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin",
      "build:main": "rollup -c rollup/config.main.js",
      "build:walk": "rollup -c rollup/config.walk.js",
      "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js",
      "build:bin": "rollup -c rollup/config.bin.js",
      "lint": "eslint src/"
    },
    "bin": {
      "acorn": "./bin/acorn"
    },
    "devDependencies": {
      "eslint": "^4.10.0",
      "eslint-config-standard": "^10.2.1",
      "eslint-plugin-import": "^2.2.0",
      "eslint-plugin-node": "^5.2.1",
      "eslint-plugin-promise": "^3.5.0",
      "eslint-plugin-standard": "^3.0.1",
      "rollup": "^0.45.0",
      "rollup-plugin-buble": "^0.16.0",
      "test262": "git+https://github.com/tc39/test262.git#18c1e799a01cc976695983b61e225ce7959bdd91",
      "test262-parser-runner": "^0.3.1",
      "unicode-10.0.0": "^0.7.5"
    }
  }

yarn manifest: 
  No manifest

Lockfile: 
  No lockfile

Trace: 
  Error: https://registry.yarnpkg.com/acorn: You must be logged in to publish packages.
      at Request.params.callback [as _callback] (/home/<USER>/.yarn/lib/cli.js:62098:18)
      at Request.self.callback (/home/<USER>/.yarn/lib/cli.js:123085:22)
      at Request.emit (events.js:160:13)
      at Request.<anonymous> (/home/<USER>/.yarn/lib/cli.js:124068:10)
      at Request.emit (events.js:160:13)
      at IncomingMessage.<anonymous> (/home/<USER>/.yarn/lib/cli.js:123988:12)
      at Object.onceWrapper (events.js:255:19)
      at IncomingMessage.emit (events.js:165:20)
      at endReadableNT (_stream_readable.js:1101:12)
      at process._tickCallback (internal/process/next_tick.js:152:19)
