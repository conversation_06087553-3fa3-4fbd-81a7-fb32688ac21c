{"name": "arrify", "version": "1.0.1", "description": "Convert a value to an array", "license": "MIT", "repository": "sindresorhus/arrify", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["array", "arr", "arrify", "arrayify", "convert", "value"], "devDependencies": {"ava": "*", "xo": "*"}}