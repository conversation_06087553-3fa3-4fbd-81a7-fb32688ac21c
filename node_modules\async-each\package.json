{"name": "async-each", "description": "No-bullshit, ultra-simple, 35-lines-of-code async parallel forEach / map function for JavaScript.", "version": "1.0.1", "license": "MIT", "keywords": ["async", "for<PERSON>ach", "each", "map", "asynchronous", "iteration", "iterate", "loop", "parallel", "concurrent", "array", "flow", "control flow"], "homepage": "https://github.com/paulmillr/async-each/", "author": "<PERSON> (http://paulmillr.com/)", "repository": "git://github.com/paulmillr/async-each.git", "main": "index.js", "dependencies": {}}