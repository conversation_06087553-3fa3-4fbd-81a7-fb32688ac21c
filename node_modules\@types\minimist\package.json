{"name": "@types/minimist", "version": "1.2.2", "description": "TypeScript definitions for minimist", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/minimist", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/Bartvds", "githubUsername": "Bartvds"}, {"name": "Necroskillz", "url": "https://github.com/Necroskillz", "githubUsername": "Necroskillz"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/kamranayub", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/minimist"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "9032205d52417d0f537f1e52af6f7ac447acb4b87dca0ab5840b83ec7818232e", "typeScriptVersion": "3.6"}