{"/Users/<USER>/git/forks/async-throttle/index.js": {"path": "/Users/<USER>/git/forks/async-throttle/index.js", "s": {"1": 1, "2": 7, "3": 1, "4": 6, "5": 6, "6": 6, "7": 6, "8": 6, "9": 6, "10": 1, "11": 1, "12": 3, "13": 13, "14": 13, "15": 13, "16": 1, "17": 19, "18": 1, "19": 45, "20": 6, "21": 39, "22": 13, "23": 13, "24": 13, "25": 13, "26": 39, "27": 18, "28": 6, "29": 6, "30": 1, "31": 6, "32": 6, "33": 6, "34": 1, "35": 13, "36": 13, "37": 1}, "b": {"1": [1, 6], "2": [6, 5], "3": [6, 5], "4": [6, 39], "5": [13, 26], "6": [18, 21], "7": [6, 0]}, "f": {"1": 7, "2": 3, "3": 13, "4": 19, "5": 45, "6": 6, "7": 13}, "fnMap": {"1": {"name": "Queue", "line": 3, "loc": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 24}}}, "2": {"name": "(anonymous_2)", "line": 22, "loc": {"start": {"line": 22, "column": 24}, "end": {"line": 22, "column": 41}}}, "3": {"name": "(anonymous_3)", "line": 23, "loc": {"start": {"line": 23, "column": 28}, "end": {"line": 23, "column": 39}}}, "4": {"name": "(anonymous_4)", "line": 31, "loc": {"start": {"line": 31, "column": 7}, "end": {"line": 31, "column": 18}}}, "5": {"name": "(anonymous_5)", "line": 36, "loc": {"start": {"line": 36, "column": 23}, "end": {"line": 36, "column": 34}}}, "6": {"name": "(anonymous_6)", "line": 55, "loc": {"start": {"line": 55, "column": 25}, "end": {"line": 55, "column": 38}}}, "7": {"name": "done", "line": 62, "loc": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 16}}}}, "statementMap": {"1": {"start": {"line": 3, "column": 0}, "end": {"line": 14, "column": 1}}, "2": {"start": {"line": 4, "column": 2}, "end": {"line": 6, "column": 3}}, "3": {"start": {"line": 5, "column": 4}, "end": {"line": 5, "column": 30}}, "4": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": 26}}, "5": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 53}}, "6": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": 19}}, "7": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 17}}, "8": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 16}}, "9": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 31}}, "10": {"start": {"line": 16, "column": 0}, "end": {"line": 20, "column": 2}}, "11": {"start": {"line": 22, "column": 0}, "end": {"line": 28, "column": 3}}, "12": {"start": {"line": 23, "column": 2}, "end": {"line": 27, "column": 4}}, "13": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 75}}, "14": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 16}}, "15": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 24}}, "16": {"start": {"line": 30, "column": 0}, "end": {"line": 34, "column": 3}}, "17": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 43}}, "18": {"start": {"line": 36, "column": 0}, "end": {"line": 53, "column": 2}}, "19": {"start": {"line": 37, "column": 2}, "end": {"line": 39, "column": 3}}, "20": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 11}}, "21": {"start": {"line": 40, "column": 2}, "end": {"line": 45, "column": 3}}, "22": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 32}}, "23": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 19}}, "24": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 20}}, "25": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 16}}, "26": {"start": {"line": 47, "column": 2}, "end": {"line": 52, "column": 3}}, "27": {"start": {"line": 48, "column": 4}, "end": {"line": 51, "column": 5}}, "28": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 30}}, "29": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 27}}, "30": {"start": {"line": 55, "column": 0}, "end": {"line": 60, "column": 2}}, "31": {"start": {"line": 56, "column": 2}, "end": {"line": 59, "column": 3}}, "32": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 22}}, "33": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 16}}, "34": {"start": {"line": 62, "column": 0}, "end": {"line": 65, "column": 1}}, "35": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 17}}, "36": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 14}}, "37": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 23}}}, "branchMap": {"1": {"line": 4, "type": "if", "locations": [{"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": 2}}, {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": 2}}]}, "2": {"line": 8, "type": "binary-expr", "locations": [{"start": {"line": 8, "column": 12}, "end": {"line": 8, "column": 19}}, {"start": {"line": 8, "column": 23}, "end": {"line": 8, "column": 25}}]}, "3": {"line": 9, "type": "binary-expr", "locations": [{"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": 40}}, {"start": {"line": 9, "column": 44}, "end": {"line": 9, "column": 52}}]}, "4": {"line": 37, "type": "if", "locations": [{"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": 2}}, {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": 2}}]}, "5": {"line": 40, "type": "if", "locations": [{"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": 2}}, {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": 2}}]}, "6": {"line": 47, "type": "if", "locations": [{"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 2}}, {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 2}}]}, "7": {"line": 56, "type": "if", "locations": [{"start": {"line": 56, "column": 2}, "end": {"line": 56, "column": 2}}, {"start": {"line": 56, "column": 2}, "end": {"line": 56, "column": 2}}]}}}}