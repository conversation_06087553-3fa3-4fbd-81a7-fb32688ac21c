<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="ccc1a0b7-8329-4ff2-b895-4d764f3c8443" name="Default" comment="">
      <change type="MODIFICATION" beforePath="$PROJECT_DIR$/scripts/build-data.js" afterPath="$PROJECT_DIR$/scripts/build-data.js" />
    </list>
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="TRACKING_ENABLED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CreatePatchCommitExecutor">
    <option name="PATCH_PATH" value="" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="default_target" />
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file leaf-file-name="build-data.js" pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/scripts/build-data.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="306">
              <caret line="99" column="59" lean-forward="true" selection-start-line="98" selection-start-column="6" selection-end-line="99" selection-end-column="59" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="index.js" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/lib/index.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="0">
              <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="index.js" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/index.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="686">
              <caret line="41" column="61" lean-forward="false" selection-start-line="41" selection-start-column="61" selection-end-line="41" selection-end-column="61" />
              <folding>
                <element signature="e#0#40#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="debug-fixtures.js" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/test/debug-fixtures.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="0">
              <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="options.json" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/test/debug-fixtures/plugins-only/options.json">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="0">
              <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="stdout.txt" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/test/debug-fixtures/plugins-only/stdout.txt">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="238">
              <caret line="14" column="42" lean-forward="false" selection-start-line="14" selection-start-column="42" selection-end-line="14" selection-end-column="42" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="options.json" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/test/debug-fixtures/built-ins/options.json">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="0">
              <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="stdout.txt" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/test/debug-fixtures/built-ins/stdout.txt">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="697">
              <caret line="41" column="28" lean-forward="false" selection-start-line="41" selection-start-column="28" selection-end-line="41" selection-end-column="28" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="EcmaScript.js" pinned="false" current-in-tab="false">
        <entry file="jar://$APPLICATION_HOME_DIR$/plugins/JavaScriptLanguage/lib/JavaScriptLanguage.jar!/com/intellij/lang/javascript/index/predefined/EcmaScript.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="340">
              <caret line="20" column="0" lean-forward="false" selection-start-line="20" selection-start-column="0" selection-end-line="20" selection-end-column="0" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="package.json" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/package.json">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="867">
              <caret line="51" column="22" lean-forward="false" selection-start-line="51" selection-start-column="22" selection-end-line="51" selection-end-column="22" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="plugins.json" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/data/plugins.json">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="612">
              <caret line="36" column="3" lean-forward="false" selection-start-line="36" selection-start-column="3" selection-end-line="36" selection-end-column="39" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="plugin-features.js" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/data/plugin-features.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="0">
              <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="environments.json" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/node_modules/compat-table/environments.json">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="26860">
              <caret line="1580" column="0" lean-forward="false" selection-start-line="1580" selection-start-column="0" selection-end-line="1580" selection-end-column="0" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="built-ins.json" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/data/built-ins.json">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="7854">
              <caret line="462" column="17" lean-forward="false" selection-start-line="462" selection-start-column="17" selection-end-line="462" selection-end-column="17" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="built-in-features.js" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/data/built-in-features.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="2074">
              <caret line="122" column="36" lean-forward="false" selection-start-line="122" selection-start-column="36" selection-end-line="122" selection-end-column="36" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="README.md" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/README.md">
          <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
            <state split_layout="SPLIT">
              <first_editor relative-caret-position="0">
                <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
                <folding>
                  <marker date="1488490578000" expanded="true" signature="9539:9785" ph="{...}" />
                  <marker date="1488490578000" expanded="true" signature="9554:9783" ph="[...]" />
                  <marker date="1488490578000" expanded="true" signature="9560:9779" ph="[...]" />
                  <marker date="1488490578000" expanded="true" signature="9568:9778" ph="{...}" />
                  <marker date="1488490578000" expanded="true" signature="9587:9651" ph="{...}" />
                </folding>
              </first_editor>
              <second_editor />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="CONTRIBUTING.md" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/CONTRIBUTING.md">
          <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
            <state split_layout="SPLIT">
              <first_editor relative-caret-position="0">
                <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
                <folding />
              </first_editor>
              <second_editor />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>ios6</find>
      <find>safari</find>
      <find>node4</find>
      <find>es6.array</find>
      <find>ios10</find>
    </findStrings>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="test" />
      </map>
    </option>
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/node_modules/compat-table/environments.json" />
        <option value="$PROJECT_DIR$/test/debug-fixtures/plugins-only/stdout.txt" />
        <option value="$PROJECT_DIR$/test/debug-fixtures/built-ins/stdout.txt" />
        <option value="$PROJECT_DIR$/package.json" />
        <option value="$PROJECT_DIR$/scripts/build-data.js" />
      </list>
    </option>
  </component>
  <component name="JsBuildToolGruntFileManager" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsBuildToolPackageJson" detection-done="true" sorting="DEFINITION_ORDER">
    <package-json value="$PROJECT_DIR$/package.json" />
  </component>
  <component name="JsGulpfileManager">
    <detection-done>true</detection-done>
    <sorting>DEFINITION_ORDER</sorting>
  </component>
  <component name="NodeModulesDirectoryManager">
    <handled-path value="$PROJECT_DIR$/node_modules" />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" backward_compatibility_performed="true" />
  <component name="ProjectFrameBounds">
    <option name="y" value="23" />
    <option name="width" value="1440" />
    <option name="height" value="812" />
  </component>
  <component name="ProjectView">
    <navigator currentView="ProjectPane" proportions="" version="1">
      <flattenPackages />
      <showMembers />
      <showModules />
      <showLibraryContents />
      <hideEmptyPackages />
      <abbreviatePackageNames />
      <autoscrollToSource />
      <autoscrollFromSource />
      <sortByType />
      <manualOrder />
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="ProjectPane">
        <subPane>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="test" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="test" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="fixtures" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="test" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="debug-fixtures" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="test" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="debug-fixtures" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="plugins-only" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="test" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="debug-fixtures" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="built-ins" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="src" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="scripts" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="babel-preset-env" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="data" />
              <option name="myItemType" value="com.jetbrains.php.projectView.PhpTreeStructureProvider$1" />
            </PATH_ELEMENT>
          </PATH>
        </subPane>
      </pane>
      <pane id="Scope" />
      <pane id="Scratches" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="settings.editor.selected.configurable" value="settings.nodejs" />
    <property name="nodejs_interpreter_path" value="$USER_HOME$/.nvm/versions/node/v4.4.6/bin/node" />
    <property name="js.eslint.eslintPackage" value="$PROJECT_DIR$/node_modules/eslint" />
    <property name="js.eslint.nodeInterpreter" value="project" />
    <property name="javascript.nodejs.core.library.configured.version" value="4.4.2" />
    <property name="settings.editor.splitter.proportion" value="0.2" />
    <property name="JavaScriptPreferStrict" value="false" />
    <property name="JavaScriptWeakerCompletionTypeGuess" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
  </component>
  <component name="RunManager">
    <configuration default="true" type="NodeJSConfigurationType" factoryName="Node.js" path-to-node="project" working-dir="">
      <method />
    </configuration>
  </component>
  <component name="ShelveChangesManager" show_recycled="false">
    <option name="remove_strategy" value="false" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ccc1a0b7-8329-4ff2-b895-4d764f3c8443" name="Default" comment="" />
      <created>1488466310672</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1488466310672</updated>
      <workItem from="1488466312550" duration="7900000" />
      <workItem from="1488557612045" duration="1214000" />
      <workItem from="1488561838691" duration="840000" />
      <workItem from="1488570231701" duration="638000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="10592000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="0" y="23" width="1440" height="812" extended-state="6" />
    <editor active="true" />
    <layout>
      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.20600858" sideWeight="0.5" order="0" side_tool="false" content_ui="combo" />
      <window_info id="TODO" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="6" side_tool="false" content_ui="tabs" />
      <window_info id="SvgViewer" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Event Log" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="7" side_tool="true" content_ui="tabs" />
      <window_info id="Database" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Version Control" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="npm" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="2" side_tool="true" content_ui="tabs" />
      <window_info id="Structure" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Terminal" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="Favorites" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="2" side_tool="true" content_ui="tabs" />
      <window_info id="Cvs" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="4" side_tool="false" content_ui="tabs" />
      <window_info id="Message" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Commander" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Inspection" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="5" side_tool="false" content_ui="tabs" />
      <window_info id="Run" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="Hierarchy" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="2" side_tool="false" content_ui="combo" />
      <window_info id="Find" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Ant Build" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Debug" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="processedProjectFiles" value="true" />
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="2678400000" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/scripts/build-data.js</url>
          <line>83</line>
        </line-breakpoint>
      </breakpoints>
      <option name="time" value="1" />
    </breakpoint-manager>
    <watches-manager />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/data/built-ins.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="7854">
          <caret line="462" column="17" lean-forward="false" selection-start-line="462" selection-start-column="17" selection-end-line="462" selection-end-column="17" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/data/built-in-features.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="2074">
          <caret line="122" column="36" lean-forward="false" selection-start-line="122" selection-start-column="36" selection-end-line="122" selection-end-column="36" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/README.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="0">
            <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
            <folding>
              <marker date="1488490578000" expanded="true" signature="9539:9785" ph="{...}" />
              <marker date="1488490578000" expanded="true" signature="9554:9783" ph="[...]" />
              <marker date="1488490578000" expanded="true" signature="9560:9779" ph="[...]" />
              <marker date="1488490578000" expanded="true" signature="9568:9778" ph="{...}" />
              <marker date="1488490578000" expanded="true" signature="9587:9651" ph="{...}" />
            </folding>
          </first_editor>
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/CONTRIBUTING.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="0">
            <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
            <folding />
          </first_editor>
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/scripts/build-data.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/debug-fixtures.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/debug-fixtures/plugins-only/options.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/debug-fixtures/plugins-only/stdout.txt">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="238">
          <caret line="14" column="42" lean-forward="false" selection-start-line="14" selection-start-column="42" selection-end-line="14" selection-end-column="42" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/debug-fixtures/built-ins/options.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/debug-fixtures/built-ins/stdout.txt">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="697">
          <caret line="41" column="28" lean-forward="false" selection-start-line="41" selection-start-column="28" selection-end-line="41" selection-end-column="28" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="jar://$APPLICATION_HOME_DIR$/plugins/JavaScriptLanguage/lib/JavaScriptLanguage.jar!/com/intellij/lang/javascript/index/predefined/EcmaScript.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="340">
          <caret line="20" column="0" lean-forward="false" selection-start-line="20" selection-start-column="0" selection-end-line="20" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/package.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="867">
          <caret line="51" column="22" lean-forward="false" selection-start-line="51" selection-start-column="22" selection-end-line="51" selection-end-column="22" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/data/plugins.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="612">
          <caret line="36" column="3" lean-forward="false" selection-start-line="36" selection-start-column="3" selection-end-line="36" selection-end-column="39" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/data/plugin-features.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/node_modules/compat-table/environments.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="26860">
          <caret line="1580" column="0" lean-forward="true" selection-start-line="1580" selection-start-column="0" selection-end-line="1580" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/data/built-ins.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="7854">
          <caret line="462" column="17" lean-forward="false" selection-start-line="462" selection-start-column="17" selection-end-line="462" selection-end-column="17" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/data/built-in-features.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="2074">
          <caret line="122" column="36" lean-forward="false" selection-start-line="122" selection-start-column="36" selection-end-line="122" selection-end-column="36" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/README.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="0">
            <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
            <folding>
              <marker date="1488490578000" expanded="true" signature="9539:9785" ph="{...}" />
              <marker date="1488490578000" expanded="true" signature="9554:9783" ph="[...]" />
              <marker date="1488490578000" expanded="true" signature="9560:9779" ph="[...]" />
              <marker date="1488490578000" expanded="true" signature="9568:9778" ph="{...}" />
              <marker date="1488490578000" expanded="true" signature="9587:9651" ph="{...}" />
            </folding>
          </first_editor>
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/CONTRIBUTING.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="0">
            <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
            <folding />
          </first_editor>
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/scripts/build-data.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/debug-fixtures.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/debug-fixtures/plugins-only/options.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/debug-fixtures/plugins-only/stdout.txt">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="238">
          <caret line="14" column="42" lean-forward="false" selection-start-line="14" selection-start-column="42" selection-end-line="14" selection-end-column="42" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/debug-fixtures/built-ins/options.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/debug-fixtures/built-ins/stdout.txt">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="697">
          <caret line="41" column="28" lean-forward="false" selection-start-line="41" selection-start-column="28" selection-end-line="41" selection-end-column="28" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="jar://$APPLICATION_HOME_DIR$/plugins/JavaScriptLanguage/lib/JavaScriptLanguage.jar!/com/intellij/lang/javascript/index/predefined/EcmaScript.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="340">
          <caret line="20" column="0" lean-forward="false" selection-start-line="20" selection-start-column="0" selection-end-line="20" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/package.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/data/plugins.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="3094">
          <caret line="182" column="16" lean-forward="false" selection-start-line="182" selection-start-column="16" selection-end-line="182" selection-end-column="16" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/data/plugin-features.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/node_modules/compat-table/environments.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="23834">
          <caret line="1402" column="23" lean-forward="false" selection-start-line="1402" selection-start-column="23" selection-end-line="1402" selection-end-column="23" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/data/built-ins.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="7854">
          <caret line="462" column="17" lean-forward="false" selection-start-line="462" selection-start-column="17" selection-end-line="462" selection-end-column="17" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/data/built-in-features.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="2074">
          <caret line="122" column="36" lean-forward="false" selection-start-line="122" selection-start-column="36" selection-end-line="122" selection-end-column="36" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/README.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="0">
            <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
            <folding>
              <marker date="1488490578000" expanded="true" signature="9539:9785" ph="{...}" />
              <marker date="1488490578000" expanded="true" signature="9554:9783" ph="[...]" />
              <marker date="1488490578000" expanded="true" signature="9560:9779" ph="[...]" />
              <marker date="1488490578000" expanded="true" signature="9568:9778" ph="{...}" />
              <marker date="1488490578000" expanded="true" signature="9587:9651" ph="{...}" />
            </folding>
          </first_editor>
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/CONTRIBUTING.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="0">
            <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
            <folding />
          </first_editor>
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/README.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="0">
            <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
            <folding>
              <marker date="1488490578000" expanded="true" signature="9539:9785" ph="{...}" />
              <marker date="1488490578000" expanded="true" signature="9554:9783" ph="[...]" />
              <marker date="1488490578000" expanded="true" signature="9560:9779" ph="[...]" />
              <marker date="1488490578000" expanded="true" signature="9568:9778" ph="{...}" />
              <marker date="1488490578000" expanded="true" signature="9587:9651" ph="{...}" />
            </folding>
          </first_editor>
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/CONTRIBUTING.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="0">
            <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
            <folding />
          </first_editor>
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/data/plugin-features.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="jar://$APPLICATION_HOME_DIR$/plugins/JavaScriptLanguage/lib/JavaScriptLanguage.jar!/com/intellij/lang/javascript/index/predefined/EcmaScript.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="340">
          <caret line="20" column="0" lean-forward="false" selection-start-line="20" selection-start-column="0" selection-end-line="20" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/data/built-in-features.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="2074">
          <caret line="122" column="36" lean-forward="false" selection-start-line="122" selection-start-column="36" selection-end-line="122" selection-end-column="36" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/data/built-ins.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="7854">
          <caret line="462" column="17" lean-forward="false" selection-start-line="462" selection-start-column="17" selection-end-line="462" selection-end-column="17" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/debug-fixtures.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/debug-fixtures/plugins-only/options.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/debug-fixtures/plugins-only/stdout.txt">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="238">
          <caret line="14" column="42" lean-forward="false" selection-start-line="14" selection-start-column="42" selection-end-line="14" selection-end-column="42" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/debug-fixtures/built-ins/options.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/debug-fixtures/built-ins/stdout.txt">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="697">
          <caret line="41" column="28" lean-forward="false" selection-start-line="41" selection-start-column="28" selection-end-line="41" selection-end-column="28" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/package.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="867">
          <caret line="51" column="22" lean-forward="false" selection-start-line="51" selection-start-column="22" selection-end-line="51" selection-end-column="22" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/node_modules/compat-table/environments.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="26860">
          <caret line="1580" column="0" lean-forward="false" selection-start-line="1580" selection-start-column="0" selection-end-line="1580" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/data/plugins.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="612">
          <caret line="36" column="3" lean-forward="false" selection-start-line="36" selection-start-column="3" selection-end-line="36" selection-end-column="39" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/index.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/index.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="686">
          <caret line="41" column="61" lean-forward="false" selection-start-line="41" selection-start-column="61" selection-end-line="41" selection-end-column="61" />
          <folding>
            <element signature="e#0#40#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/scripts/build-data.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="306">
          <caret line="99" column="59" lean-forward="true" selection-start-line="98" selection-start-column="6" selection-end-line="99" selection-end-column="59" />
          <folding />
        </state>
      </provider>
    </entry>
  </component>
</project>